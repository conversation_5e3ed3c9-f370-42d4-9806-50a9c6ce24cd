package user_request

// LoginReq 登录请求结构体
type LoginReq struct {
	Username string `json:"username" validate:"required,min=3,max=20" comment:"用户名"`
	Password string `json:"password" validate:"required,min=6" comment:"密码"`
}

// LoginRsp 登录响应结构体
type LoginRsp struct {
	Token    string `json:"token" comment:"访问令牌"`
	UserID   int64  `json:"userId" comment:"用户ID"`
	Username string `json:"username" comment:"用户名"`
	Email    string `json:"email" comment:"邮箱"`
}
