// 生成一个go的StudentUser实体类，使用playGround/validator做参数验证，要求：
// 1、ID字段,主键自增
// 2、UserName，5-20个字符，只允许：字母、数字、下划线
// 3、密码，不能为空
// 4、Email，不能为空，格式正确，默认值是""，3-254个字符
// 5、Phone，不能为空，格式正确，默认值是""，8-15 位数字
// 6、Status，不能为空，值为0表示禁用，1表示启用，默认值是1
// 7、Points，用户积分，不为空，默认值为0
// 8、CreateTime，自动采用当前时间，仅在创建的时候设置
// 9、UpdateTime，自动采用当前时间，每次更新的时候设置

// CREATE TABLE `tb_user` (
//   `id` int unsigned NOT NULL AUTO_INCREMENT,
//   `user_name` varchar(20) NOT NULL,
//   `password` varchar(255) NOT NULL,
//   `email` varchar(254) NOT NULL DEFAULT '',
//   `phone` varchar(15) NOT NULL DEFAULT '',
//   `status` tinyint NOT NULL DEFAULT '1',
//   `points` int NOT NULL DEFAULT '0',
//   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
//   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
//   PRIMARY KEY (`id`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学生用户表';

package entity

import (
	"time"
)

// StudentUser 学生用户表模型
type User struct {
	ID         uint      `gorm:"primaryKey;autoIncrement;column:id;comment:主键ID" json:"id"`
	UserName   string    `gorm:"type:varchar(20);not null;column:user_name;comment:用户名" json:"user_name"`
	Password   string    `gorm:"type:varchar(255);not null;column:password;comment:密码（加密存储）" json:"-"`
	Email      string    `gorm:"type:varchar(254);not null;default:'';column:email;comment:邮箱" json:"email"`
	Phone      string    `gorm:"type:varchar(15);not null;default:'';column:phone;comment:手机号" json:"phone"`
	Status     int8      `gorm:"type:tinyint;not null;default:1;column:status;comment:状态(1正常 0禁用)" json:"status"`
	Points     int       `gorm:"type:int;not null;default:0;column:points;comment:积分" json:"points"`
	CreateTime time.Time `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP;column:create_time;comment:创建时间" json:"create_time"`
	UpdateTime time.Time `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:update_time;comment:更新时间" json:"update_time"`
}

// TableName 指定表名（GORM 默认使用结构体名的蛇形复数作为表名，此处明确指定）
func (User) TableName() string {
	return "tb_user"
}
