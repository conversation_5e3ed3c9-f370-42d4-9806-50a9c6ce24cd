package common

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

var Logger *log.Logger

// DailyRotateWriter 按天自动分割的日志写入器
type DailyRotateWriter struct {
	logDir        string
	currentDate   string
	currentFile   *os.File
	retentionDays int // 日志保留天数
	mu            sync.Mutex
}

// NewDailyRotateWriter 创建新的日志分割写入器
func NewDailyRotateWriter(logDir string) (*DailyRotateWriter, error) {
	return NewDailyRotateWriterWithRetention(logDir, 15) // 默认保留15天
}

// NewDailyRotateWriterWithRetention 创建新的日志分割写入器（指定保留天数）
func NewDailyRotateWriterWithRetention(logDir string, retentionDays int) (*DailyRotateWriter, error) {
	// 创建日志目录
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	writer := &DailyRotateWriter{
		logDir:        logDir,
		retentionDays: retentionDays,
	}

	// 初始化当前日志文件
	if err := writer.rotateIfNeeded(); err != nil {
		return nil, err
	}

	return writer, nil
}

// Write 实现 io.Writer 接口
func (w *DailyRotateWriter) Write(p []byte) (n int, err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	// 检查是否需要切换日志文件
	if err := w.rotateIfNeeded(); err != nil {
		return 0, err
	}

	// 写入日志数据
	return w.currentFile.Write(p)
}

// rotateIfNeeded 检查并在需要时切换日志文件
func (w *DailyRotateWriter) rotateIfNeeded() error {
	today := time.Now().Format("2006-01-02")

	// 如果日期没有变化且文件已打开，直接返回
	if today == w.currentDate && w.currentFile != nil {
		return nil
	}

	// 关闭旧文件
	if w.currentFile != nil {
		if err := w.currentFile.Close(); err != nil {
			log.Printf("关闭旧日志文件失败: %v", err)
		}
	}

	// 更新当前日期
	w.currentDate = today

	// 生成新的日志文件路径
	logFile := filepath.Join(w.logDir, fmt.Sprintf("%s.log", today))

	// 打开新的日志文件
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	w.currentFile = file

	// 清理过期的日志文件
	go w.cleanupOldLogs()

	return nil
}

// cleanupOldLogs 清理过期的日志文件
func (w *DailyRotateWriter) cleanupOldLogs() {
	if w.retentionDays <= 0 {
		return // 如果保留天数<=0，不进行清理
	}

	// 计算截止日期
	cutoffDate := time.Now().AddDate(0, 0, -w.retentionDays)

	// 读取日志目录
	files, err := os.ReadDir(w.logDir)
	if err != nil {
		log.Printf("读取日志目录失败: %v", err)
		return
	}

	cleanedCount := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// 检查是否为日志文件（格式：YYYY-MM-DD.log）
		fileName := file.Name()
		matched, err := filepath.Match("????-??-??.log", fileName)
		if err != nil || !matched {
			continue
		}

		// 提取日期部分
		dateStr := fileName[:10] // 取前10个字符 YYYY-MM-DD
		fileDate, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			continue // 跳过无法解析日期的文件
		}

		// 如果文件日期早于截止日期，删除它
		if fileDate.Before(cutoffDate) {
			filePath := filepath.Join(w.logDir, fileName)
			if err := os.Remove(filePath); err != nil {
				log.Printf("删除过期日志文件失败 %s: %v", fileName, err)
			} else {
				cleanedCount++
				log.Printf("已删除过期日志文件: %s", fileName)
			}
		}
	}

	if cleanedCount > 0 {
		log.Printf("日志清理完成，共删除 %d 个过期文件（保留 %d 天）", cleanedCount, w.retentionDays)
	}
}

// Close 关闭日志写入器
func (w *DailyRotateWriter) Close() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.currentFile != nil {
		return w.currentFile.Close()
	}
	return nil
}

// InitLogger 初始化日志配置
func InitLogger() error {
	// 创建按天分割的日志写入器
	dailyWriter, err := NewDailyRotateWriter("logs")
	if err != nil {
		return err
	}

	// 创建多输出写入器（同时输出到文件和控制台）
	multiWriter := io.MultiWriter(dailyWriter, os.Stdout)

	// 配置日志输出
	Logger = log.New(multiWriter, "",
		log.Ldate|log.Ltime|log.Lshortfile)

	Logger.Printf("日志系统初始化成功，支持按天自动分割")
	return nil
}

// CloseLogger 关闭日志系统
func CloseLogger() error {
	// 这里可以添加清理逻辑，比如关闭文件句柄
	Logger.Printf("日志系统正在关闭...")
	return nil
}
