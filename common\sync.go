package common

import "sync"

type GenericMap[K comparable, V any] struct {
	m sync.Map
}

func NewGenericMap[K comparable, V any]() *GenericMap[K, V] {
	return &GenericMap[K, V]{
		m: sync.Map{},
	}
}

func (gm *GenericMap[K, V]) Set(key K, value V) {
	gm.m.Store(key, value)
}

func (gm *GenericMap[K, V]) Get(key K) (V, bool) {
	value, ok := gm.m.Load(key)
	if ok {
		return value.(V), true
	}
	var zero V
	return zero, false
}

func (gm *GenericMap[K, V]) Delete(key K) {
	gm.m.Delete(key)
}

func (gm *GenericMap[K, V]) Range(f func(key K, value V) bool) {
	gm.m.Range(func(k, v any) bool {
		return f(k.(K), v.(V))
	})
}

type GenericSet[T comparable] struct {
	size int
	m sync.Map
}

func NewGenericSet[T comparable]() *GenericSet[T] {
	return &GenericSet[T]{
		size: 0,
	}
}

func (gs *GenericSet[T]) Add(value T) {
	gs.m.Store(value, struct{}{}) // 使用空结构体作为值
	gs.size++
}

func (gs *GenericSet[T]) Remove(value T) {
	gs.m.Delete(value)
	gs.size--
}

func (gs *GenericSet[T]) Contains(value T) bool {
	_, ok := gs.m.Load(value)
	return ok
}

func (gs *GenericSet[T]) Range(f func(value T) bool) {
	gs.m.Range(func(k, v any) bool {
		return f(k.(T))
	})
}

func (gs *GenericSet[T]) Size() int {
	return gs.size
}
