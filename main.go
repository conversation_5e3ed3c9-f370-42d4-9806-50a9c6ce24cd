package main

import (
	"context"
	"study_platform/config"
	"study_platform/entity/query"

	"study_platform/entity"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 初始化数据库连接
	db, err := gorm.Open(mysql.Open(config.C.Database.GetDSN()))
	if err != nil {
		panic(err)
	}
	query.SetDefault(db)
	//q := query.Use(db)

	ctx := context.Background()

	q := query.Q.User.WithContext(ctx)

	q.Create(&entity.User{
		UserName: "test2",
		Password: "test2",
		Email:    "<EMAIL>",
		Phone:    "123425678901",
		Status:   1,
		Points:   0,
	})

	// q.Q.User.WithContext(ctx).Create(&entity.User{
	// 	UserName: "test",
	// 	Password: "test",
	// 	Email:    "<EMAIL>",
	// 	Phone:    "12345678901",
	// 	Status:   1,
	// 	Points:   0,
	// })

	// q.WithContext(ctx).User.Create(&entity.User{
	// 	UserName: "test",
	// 	Password: "test",
	// 	Email:    "<EMAIL>",
	// 	Phone:    "12345678901",
	// 	Status:   1,
	// 	Points:   0,
	// })

	// app := fiber.New()

	// api.UseUserAPI(app)

	// log.Fatal(app.Listen(":8080"))
}
