// CREATE TABLE `tb_user_rpa` (
//     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
//     `user_id` BIGINT NOT NULL COMMENT '用户id',
//     `rpa_status` INT NOT NULL DEFAULT 0 COMMENT '实名状态',
//     `real_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
//     `id_number` VARCHAR(18) NOT NULL DEFAULT '' COMMENT '身份证号',
//     `id_front` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '身份证正面',
//     `id_back` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '身份证反面',
//     `id_with_hand` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '手持身份证',
//     `face_image` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '人脸图片',
//     `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//     `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
//     PRIMARY KEY (`id`),
//     UNIQUE KEY `uk_user_id` (`user_id`),
//     INDEX `idx_rpa_status` (`rpa_status`),
//     INDEX `idx_id_number` (`id_number`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户实名认证表';

package entity

import (
	"study_platform/core"
	"time"
)

type UserRPA struct {
	ID        int64          `gorm:"column:id;primaryKey" json:"id"`              // 主键id
	UserID    int64          `gorm:"column:user_id;not null" json:"userId"`       // 用户id
	RpaStatus core.RPAStatus `gorm:"column:rpa_status;not null" json:"rpaStatus"` // 实名状态

	//实名信息
	RealName     string `gorm:"column:real_name;not null" json:"realName"`      // 真实姓名
	IDNumber     string `gorm:"column:id_number;not null" json:"idNumber"`      // 身份证号
	IdCardFront  string `gorm:"column:id_front;not null" json:"idFront"`        // 身份证正面
	IdCardBack   string `gorm:"column:id_back;not null" json:"idBack"`          // 身份证反面
	IdCardInHand string `gorm:"column:id_with_hand;not null" json:"idWithHand"` // 手持身份证

	//人脸信息
	FaceImage string `gorm:"column:face_image;not null" json:"faceImage"` // 人脸图片

	CreatedAt *time.Time `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
}

// TableName 指定表名（GORM 默认使用结构体名的蛇形复数作为表名，此处明确指定）
func (UserRPA) TableName() string {
	return "tb_user_rpa"
}
