// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"fmt"
	"study_platform/entity"
	"testing"

	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm/clause"
)

func init() {
	InitializeDB()
	err := _gen_test_db.AutoMigrate(&entity.UserParams{})
	if err != nil {
		fmt.Printf("Error: AutoMigrate(&entity.UserParams{}) fail: %s", err)
	}
}

func Test_userParamsQuery(t *testing.T) {
	userParams := newUserParams(_gen_test_db)
	userParams = *userParams.As(userParams.TableName())
	_do := userParams.WithContext(context.Background()).Debug()

	primaryKey := field.NewString(userParams.TableName(), clause.PrimaryKey)
	_, err := _do.Unscoped().Where(primaryKey.IsNotNull()).Delete()
	if err != nil {
		t.Error("clean table <m_user_params> fail:", err)
		return
	}

	_, ok := userParams.GetFieldByName("")
	if ok {
		t.Error("GetFieldByName(\"\") from userParams success")
	}

	err = _do.Create(&entity.UserParams{})
	if err != nil {
		t.Error("create item in table <m_user_params> fail:", err)
	}

	err = _do.Save(&entity.UserParams{})
	if err != nil {
		t.Error("create item in table <m_user_params> fail:", err)
	}

	err = _do.CreateInBatches([]*entity.UserParams{{}, {}}, 10)
	if err != nil {
		t.Error("create item in table <m_user_params> fail:", err)
	}

	_, err = _do.Select(userParams.ALL).Take()
	if err != nil {
		t.Error("Take() on table <m_user_params> fail:", err)
	}

	_, err = _do.First()
	if err != nil {
		t.Error("First() on table <m_user_params> fail:", err)
	}

	_, err = _do.Last()
	if err != nil {
		t.Error("First() on table <m_user_params> fail:", err)
	}

	_, err = _do.Where(primaryKey.IsNotNull()).FindInBatch(10, func(tx gen.Dao, batch int) error { return nil })
	if err != nil {
		t.Error("FindInBatch() on table <m_user_params> fail:", err)
	}

	err = _do.Where(primaryKey.IsNotNull()).FindInBatches(&[]*entity.UserParams{}, 10, func(tx gen.Dao, batch int) error { return nil })
	if err != nil {
		t.Error("FindInBatches() on table <m_user_params> fail:", err)
	}

	_, err = _do.Select(userParams.ALL).Where(primaryKey.IsNotNull()).Order(primaryKey.Desc()).Find()
	if err != nil {
		t.Error("Find() on table <m_user_params> fail:", err)
	}

	_, err = _do.Distinct(primaryKey).Take()
	if err != nil {
		t.Error("select Distinct() on table <m_user_params> fail:", err)
	}

	_, err = _do.Select(userParams.ALL).Omit(primaryKey).Take()
	if err != nil {
		t.Error("Omit() on table <m_user_params> fail:", err)
	}

	_, err = _do.Group(primaryKey).Find()
	if err != nil {
		t.Error("Group() on table <m_user_params> fail:", err)
	}

	_, err = _do.Scopes(func(dao gen.Dao) gen.Dao { return dao.Where(primaryKey.IsNotNull()) }).Find()
	if err != nil {
		t.Error("Scopes() on table <m_user_params> fail:", err)
	}

	_, _, err = _do.FindByPage(0, 1)
	if err != nil {
		t.Error("FindByPage() on table <m_user_params> fail:", err)
	}

	_, err = _do.ScanByPage(&entity.UserParams{}, 0, 1)
	if err != nil {
		t.Error("ScanByPage() on table <m_user_params> fail:", err)
	}

	_, err = _do.Attrs(primaryKey).Assign(primaryKey).FirstOrInit()
	if err != nil {
		t.Error("FirstOrInit() on table <m_user_params> fail:", err)
	}

	_, err = _do.Attrs(primaryKey).Assign(primaryKey).FirstOrCreate()
	if err != nil {
		t.Error("FirstOrCreate() on table <m_user_params> fail:", err)
	}

	var _a _another
	var _aPK = field.NewString(_a.TableName(), "id")

	err = _do.Join(&_a, primaryKey.EqCol(_aPK)).Scan(map[string]interface{}{})
	if err != nil {
		t.Error("Join() on table <m_user_params> fail:", err)
	}

	err = _do.LeftJoin(&_a, primaryKey.EqCol(_aPK)).Scan(map[string]interface{}{})
	if err != nil {
		t.Error("LeftJoin() on table <m_user_params> fail:", err)
	}

	_, err = _do.Not().Or().Clauses().Take()
	if err != nil {
		t.Error("Not/Or/Clauses on table <m_user_params> fail:", err)
	}
}
