package common

import (
	"errors"
	"runtime/debug"
	"strings"

	"study_platform/cache"
	"study_platform/constant"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

// RecoveryMiddleware 是一个中间件，用于捕获 panic
func GlobalRecoveryMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 使用 defer 来捕获 panic
		defer func() {
			if err := recover(); err != nil {
				// 打印错误信息和堆栈跟踪
				stackTrace := string(debug.Stack())
				Logger.Printf("Panic occurred:\nError: %v\nStack Trace:\n%s\n", err, stackTrace)

				// 获取当前请求的路径和方法
				path := c.Path()
				method := c.Method()
				Logger.Printf("Request details:\nMethod: %s\nPath: %s\n", method, path)

				// 返回 500 错误
				ServerErrorRsp(c, err.(error))
			}
		}()

		// 继续处理请求
		return c.Next()
	}
}

func NotFoundHandler(c *fiber.Ctx) error {
	if c.Response().StatusCode() == fiber.StatusNotFound {
		Logger.Println("Redirecting to /index.html")
		return c.Redirect("/index.html", fiber.StatusFound)
	}
	return c.Next()
}

// 获取真实客户端IP的函数
func GetRealIP(c *fiber.Ctx) string {
	// 优先从 X-Forwarded-For 获取
	if ip := c.Get("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For 可能包含多个IP，第一个是客户端真实IP
		ips := strings.Split(ip, ",")
		if len(ips) > 0 && ips[0] != "" {
			return strings.TrimSpace(ips[0])
		}
	}

	// 尝试从 X-Real-IP 获取
	if ip := c.Get("X-Real-IP"); ip != "" {
		return ip
	}

	// 如果都没有，则返回直接连接的IP
	return c.IP()
}

func GetBodyRequestNoAuth[T any](c *fiber.Ctx) (reqRet *T, err error) {
	// 解析 JSON 数据
	var req T
	if err := c.BodyParser(&req); err != nil {
		return reqRet, err
	}

	// 校验数据
	if err := GetValidate().Struct(req); err != nil {
		msg := ""
		for _, err := range err.(validator.ValidationErrors) {
			// 获取自定义错误消息
			msg += err.Translate(GetTrans()) + "\n"
		}
		return reqRet, errors.New(msg)
	}

	return &req, nil
}

type SessionProvider interface {
	GetSession(userId int64, platformType string) *UserSession
}

func GetBodyRequest[T any](c *fiber.Ctx) (reqRet *T, session *UserSession, err error) {
	// 解析 JSON 数据
	var req *T = new(T)
	if err := c.BodyParser(req); err != nil {
		return nil, nil, err
	}

	// 校验数据
	if err := GetValidate().Struct(req); err != nil {
		msg := ""
		for _, err := range err.(validator.ValidationErrors) {
			// 获取自定义错误消息
			msg += err.Translate(GetTrans()) + "\n"
		}
		return nil, nil, errors.New(msg)
	}

	// 设置会话
	uuid := c.Get("S-SECRETE-TOKEN")
	if uuid == "" {
		return req, nil, errors.New("用户未登录")
	}

	sessionData, err := cache.C.Get(constant.LOGIN_TOKEN_KEY + uuid)
	if err != nil {
		return req, nil, errors.New("获取用户会话失败: " + err.Error())
	}
	session, ok := sessionData.(*UserSession)
	if !ok {
		return req, nil, errors.New("用户会话数据格式错误")
	}

	if session == nil {
		return req, nil, errors.New("用户会话已经过期")
	}

	if session.UUID != uuid {
		return req, nil, errors.New("Token错误,您可能已经在其它地点登录")
	}

	return req, session, nil
}
