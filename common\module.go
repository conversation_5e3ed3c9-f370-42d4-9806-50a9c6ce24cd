package common

type ModuleContext struct {
	context map[string]interface{}
}

func NewModuleContext() *ModuleContext {
	return &ModuleContext{
		context: make(map[string]interface{}),
	}
}

func (m *ModuleContext) Get(key string) interface{} {
	return m.context[key]
}

func (m *ModuleContext) Set(key string, value interface{}) {
	m.context[key] = value
}

type Module struct {
	ModuleName string
	ModuleType string

	InitFunc func(context *ModuleContext)

	StartFunc func(context *ModuleContext)

	StopFunc func(context *ModuleContext)
}

func (m *Module) Init(context *ModuleContext) {
	m.InitFunc(context)
}

func (m *Module) Start(context *ModuleContext) {
	m.StartFunc(context)
}

func (m *Module) Stop(context *ModuleContext) {
	m.StopFunc(context)
}

func (m *Module) GetModuleName() string {
	return m.ModuleName
}

func (m *Module) GetModuleType() string {
	return m.ModuleType
}

type ModuleManager struct {
	context *ModuleContext
	modules map[string]*Module
}

var moduleManager *ModuleManager = &ModuleManager{
	modules: make(map[string]*Module),
	context: NewModuleContext(),
}

func GetModuleManager() *ModuleManager {
	return moduleManager
}

func (m *ModuleManager) AddModule(module *Module) {
	m.modules[module.ModuleName] = module
}

func (m *ModuleManager) GetModule(moduleName string) *Module {
	return m.modules[moduleName]
}

func (m *ModuleManager) InitModules() {
	for _, module := range m.modules {
		if module.InitFunc != nil {
			module.Init(m.context)
		}
	}
}

func (m *ModuleManager) StartModules() {
	for _, module := range m.modules {
		if module.StartFunc != nil {
			module.Start(m.context)
		}
	}
}

func (m *ModuleManager) StopModules() {
	for _, module := range m.modules {
		if module.StopFunc != nil {
			module.Stop(m.context)
		}
	}
}
