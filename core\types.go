package core

type EnumInfo struct {
	key   string
	label string
	value int
}

type ConfType int16

const (
	ConfType_String ConfType = iota
	ConfType_Int
)

var (
	ConfType_info = map[int8]*EnumInfo{
		1: {key: "String", label: "字符串", value: 1},
		2: {key: "Int", label: "整数", value: 2},
	}
)

func (x ConfType) String() string {
	if info, exists := ConfType_info[int8(x)]; exists {
		return info.label
	}
	return "未知"
}

func (x ConfType) Desc() string {
	if info, exists := ConfType_info[int8(x)]; exists {
		return info.label
	}
	return "未知"
}

func (x ConfType) Value() int {
	if info, exists := ConfType_info[int8(x)]; exists {
		return info.value
	}
	return 0
}

type UserParamKey string

const (
	UserParamKey_NickName UserParamKey = "user.imageurl"
)

var (
	UserParamKey_info = map[string]*EnumInfo{
		"user.imageurl": {key: "Image", label: "用户头像", value: 1},
	}
)

func (x UserParamKey) String() string {
	if info, exists := UserParamKey_info[string(x)]; exists {
		return info.key
	}
	return "未知"
}

func (x UserParamKey) Desc() string {
	if info, exists := UserParamKey_info[string(x)]; exists {
		return info.label
	}
	return "未知"
}

func (x UserParamKey) Value() int {
	if info, exists := UserParamKey_info[string(x)]; exists {
		return info.value
	}
	return 0
}

type RPAStatus int16

const (
	RPA_NONE             RPAStatus = iota
	RPA_ID_INFO_UPLOADED           // 身份信息已上传
	RPA_ID_INFO_APPROVED           // 身份信息已审核
	RPA_ID_INFO_REJECTED           // 身份信息已拒绝
	RPA_ID_INFO_EXPIRED            // 身份信息已过期

	RPA_FACE_CHECKED  // 人脸信息已审核
	RPA_FACE_REJECTED // 人脸信息已拒绝
	RPA_FACE_EXPIRED  // 人脸信息已过期

	RPA_FINISHED // 完成
)

var (
	RPAStatus_info = map[int16]*EnumInfo{
		0: {key: "RPAStatus_info", label: "实名未开始", value: 0},
		1: {key: "RPA_ID_INFO_UPLOADED", label: "身份信息已上传", value: 1},
		2: {key: "RPA_ID_INFO_APPROVED", label: "身份信息已审核", value: 2},
		3: {key: "RPA_ID_INFO_REJECTED", label: "身份信息已拒绝", value: 3},
		4: {key: "RPA_ID_INFO_EXPIRED", label: "身份信息已过期", value: 4},
		5: {key: "RPA_FACE_CHECKED", label: "人脸信息已审核", value: 5},
		6: {key: "RPA_FACE_REJECTED", label: "人脸信息已拒绝", value: 6},
		7: {key: "RPA_FACE_EXPIRED", label: "人脸信息已过期", value: 7},
		8: {key: "RPA_FINISHED", label: "完成", value: 8},
	}
)

func (x RPAStatus) String() string {
	if info, exists := RPAStatus_info[int16(x)]; exists {
		return info.key
	}
	return "未知"
}

func (x RPAStatus) Desc() string {
	if info, exists := RPAStatus_info[int16(x)]; exists {
		return info.label
	}
	return "未知"
}

func (x RPAStatus) Value() int {
	if info, exists := RPAStatus_info[int16(x)]; exists {
		return info.value
	}
	return 0
}
