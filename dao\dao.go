package dao

import (
	"fmt"
	"study_platform/config"
	"study_platform/entity/query"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Context struct {
	DB *gorm.DB
	//UserDao *UserDao
}

func NewContext() *Context {
	return &Context{}
}

var ctx *Context = NewContext()

func init() {
	// 初始化数据库连接
	db, err := gorm.Open(mysql.Open(config.C.Database.GetDSN()))
	if err != nil {
		panic(err)
	}
	ctx.DB = db
	query.SetDefault(db)

	fmt.Println("aaaa")
}
