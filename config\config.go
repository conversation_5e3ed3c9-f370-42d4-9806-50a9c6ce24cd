package config

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
)

// Config 应用配置
type Config struct {
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
}

var C *Config = NewConfig()

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Username string `json:"username"` // 数据库用户名
	Password string `json:"password"` // 数据库密码
	Host     string `json:"host"`     // 数据库主机
	Port     string `json:"port"`     // 数据库端口
	DBName   string `json:"db_name"`  // 数据库名称
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`     // Redis主机
	Port     string `json:"port"`     // Redis端口
	Password string `json:"password"` // Redis密码
}

// GetDSN 获取数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		d.Username, d.Password, d.Host, d.Port, d.DBName)
}

// GetRedisAddr 获取Redis地址
func (r *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", r.Host, r.Port)
}

// NewConfig 从.env文件创建新的配置对象
func NewConfig() *Config {
	// 加载.env文件
	envVars := loadEnvFile(".env")

	return &Config{
		Database: DatabaseConfig{
			Username: getEnvValue(envVars, "DB_USERNAME", "root"),
			Password: getEnvValue(envVars, "DB_PASSWORD", ""),
			Host:     getEnvValue(envVars, "DB_HOST", "localhost"),
			Port:     getEnvValue(envVars, "DB_PORT", "3306"),
			DBName:   getEnvValue(envVars, "DB_NAME", "learn3d"),
		},
		Redis: RedisConfig{
			Host:     getEnvValue(envVars, "REDIS_HOST", "localhost"),
			Port:     getEnvValue(envVars, "REDIS_PORT", "6379"),
			Password: getEnvValue(envVars, "REDIS_PASSWORD", ""),
		},
	}
}

// loadEnvFile 加载.env文件并返回键值对映射
func loadEnvFile(filename string) map[string]string {
	envVars := make(map[string]string)

	file, err := os.Open(filename)
	if err != nil {
		log.Printf("警告: 无法打开.env文件: %v", err)
		return envVars
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析 KEY=VALUE 格式
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			// 移除值两端的引号
			value = removeQuotes(value)

			envVars[key] = value
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("警告: 读取.env文件时出错: %v", err)
	}

	log.Printf("成功加载.env文件，共读取 %d 个配置项", len(envVars))
	return envVars
}

// getEnvValue 获取环境变量值，优先级：环境变量 > .env文件 > 默认值
func getEnvValue(envVars map[string]string, key, defaultValue string) string {
	// 首先检查系统环境变量
	if value := os.Getenv(key); value != "" {
		return value
	}

	// 然后检查.env文件中的值
	if value, exists := envVars[key]; exists {
		return value
	}

	// 最后返回默认值
	return defaultValue
}

// removeQuotes 移除字符串两端的引号
func removeQuotes(s string) string {
	if len(s) >= 2 {
		if (s[0] == '"' && s[len(s)-1] == '"') || (s[0] == '\'' && s[len(s)-1] == '\'') {
			return s[1 : len(s)-1]
		}
	}
	return s
}
