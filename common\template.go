package common

import (
	"bytes"
	"html/template"
	"io"
)

// TemplateEngine 模板引擎结构体
type TemplateEngine struct {
	templates *template.Template
}

// NewTemplateEngine 创建新的模板引擎
func NewTemplateEngine() *TemplateEngine {
	return &TemplateEngine{
		templates: template.New(""),
	}
}

// ParseFiles 解析模板文件
func (e *TemplateEngine) ParseFiles(files ...string) error {
	var err error
	e.templates, err = e.templates.ParseFiles(files...)
	return err
}

// Parse 解析模板字符串
func (e *TemplateEngine) Parse(name, text string) error {
	var err error
	e.templates, err = e.templates.New(name).Parse(text)
	return err
}

// Execute 执行模板渲染
func (e *TemplateEngine) Execute(wr io.Writer, name string, data interface{}) error {
	return e.templates.ExecuteTemplate(wr, name, data)
}

// ExecuteToString 执行模板渲染并返回字符串
func (e *TemplateEngine) ExecuteToString(name string, data interface{}) (string, error) {
	var buf bytes.Buffer
	err := e.Execute(&buf, name, data)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

// Example usage:
/*
// 创建模板引擎
engine := template.NewTemplateEngine()

// 解析模板文件
err := engine.ParseFiles("templates/layout.html", "templates/content.html")
if err != nil {
    log.Fatal(err)
}

// 解析模板字符串
err = engine.Parse("greeting", "Hello, {{.Name}}!")
if err != nil {
    log.Fatal(err)
}

// 准备数据
data := struct {
    Name string
}{
    Name: "World",
}

// 渲染到字符串
result, err := engine.ExecuteToString("greeting", data)
if err != nil {
    log.Fatal(err)
}
fmt.Println(result) // 输出: Hello, World!
*/
