package util

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// 全局JWT工具实例
var JWT *JWTUtil

// 初始化全局JWT工具
func init() {
	// 默认配置
	secretKey := "carmazhaoIsGoodTeacher"
	issuer := "learn3d"
	expireIn := 24 * time.Hour // 24小时过期

	JWT = NewJWTUtil(secretKey, issuer, expireIn)
}

// JWTClaims JWT声明结构体
type JWTClaims struct {
	UUID string `json:"uuid"`
	jwt.RegisteredClaims
}

// JWTUtil JWT工具类
type JWTUtil struct {
	secretKey []byte
	issuer    string
	expireIn  time.Duration
}

// NewJWTUtil 创建新的JWT工具实例
func NewJWTUtil(secretKey, issuer string, expireIn time.Duration) *JWTUtil {
	return &JWTUtil{
		secretKey: []byte(secretKey),
		issuer:    issuer,
		expireIn:  expireIn,
	}
}

// GenerateToken 生成JWT令牌
func (j *JWTUtil) GenerateToken(uuid string) (string, error) {
	now := time.Now()

	claims := JWTClaims{
		UUID: uuid,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   uuid,
			Audience:  []string{"web", "mobile"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expireIn)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// ParseToken 解析JWT令牌
func (j *JWTUtil) ParseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// ValidateToken 验证JWT令牌是否有效
func (j *JWTUtil) ValidateToken(tokenString string) bool {
	_, err := j.ParseToken(tokenString)
	return err == nil
}

// RefreshToken 刷新JWT令牌
func (j *JWTUtil) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查令牌是否即将过期（在过期前30分钟内可以刷新）
	if time.Until(claims.ExpiresAt.Time) > 30*time.Minute {
		return "", errors.New("令牌尚未到刷新时间")
	}

	// 生成新令牌
	return j.GenerateToken(claims.UUID)
}

// GetUUIDFromToken 从令牌中获取UUID
func (j *JWTUtil) GetUUIDFromToken(tokenString string) (string, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.UUID, nil
}

// IsTokenExpired 检查令牌是否已过期
func (j *JWTUtil) IsTokenExpired(tokenString string) bool {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return true
	}
	return time.Now().After(claims.ExpiresAt.Time)
}

// GetTokenExpireTime 获取令牌过期时间
func (j *JWTUtil) GetTokenExpireTime(tokenString string) (time.Time, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}
	return claims.ExpiresAt.Time, nil
}
