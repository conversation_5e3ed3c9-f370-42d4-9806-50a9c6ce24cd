package api

import (
	"github.com/gofiber/fiber/v2"
)

type LoginReq struct {
	UserName string `json:"userName" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type LoginRsp struct {
	Token string `json:"token"`
}

type LoginAPI struct {
}

var loginAPI *LoginAPI = &LoginAPI{}

func (l *LoginAPI) PasswordLogin(c *fiber.Ctx, req *LoginReq) (*LoginRsp, error) {
	// TODO: 实现登录逻辑
	return &LoginRsp{
		Token: "sample_token",
	}, nil
}
