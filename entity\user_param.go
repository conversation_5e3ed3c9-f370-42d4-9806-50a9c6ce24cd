package entity

import (
	"study_platform/core"
	"time"
)

/*
CREATE TABLE `m_user_params` (
	`id` bigint NOT NULL COMMENT '主键id',
	`user_id` bigint NOT NULL COMMENT '用户id',
	`type` int NOT NULL COMMENT '类型',
	`key` varchar(255) NOT NULL COMMENT '键',
	`value` varchar(1024) NOT NULL COMMENT '值',
	`int_val` bigint NOT NULL DEFAULT '0' COMMENT '整数值',
	`comment` varchar(512) NOT NULL COMMENT '备注',
	`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`id`),
	UNIQUE KEY `uk_user_id_type_key` (`user_id`, `type`, `key`),
	KEY `idx_type` (`type`),
	KEY `idx_key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户参数表';
*/

// UserParams 用户参数实体类
type UserParams struct {
	ID        int64             `gorm:"column:id;primaryKey" json:"id"`                    // 主键id
	UserID    int64             `gorm:"column:user_id;not null" json:"userId"`             // 用户id
	Type      core.ConfType     `gorm:"column:type;not null" json:"type"`                  // 类型
	Key       core.UserParamKey `gorm:"column:key;not null" json:"key"`                    // 键
	Val       string            `gorm:"column:value;not null" json:"value"`                // 值
	IntVal    int64             `gorm:"column:int_val;not null" json:"intVal"`             // 整数值
	Comment   string            `gorm:"column:comment;not null" json:"comment"`            // 备注
	CreatedAt *time.Time        `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt *time.Time        `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
}

// TableName 返回表名
func (UserParams) TableName() string {
	return "m_user_params"
}
