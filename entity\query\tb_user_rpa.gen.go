// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"
	"study_platform/entity"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newUserRPA(db *gorm.DB, opts ...gen.DOOption) userRPA {
	_userRPA := userRPA{}

	_userRPA.userRPADo.UseDB(db, opts...)
	_userRPA.userRPADo.UseModel(&entity.UserRPA{})

	tableName := _userRPA.userRPADo.TableName()
	_userRPA.ALL = field.NewAsterisk(tableName)
	_userRPA.ID = field.NewInt64(tableName, "id")
	_userRPA.UserID = field.NewInt64(tableName, "user_id")
	_userRPA.RpaStatus = field.NewInt16(tableName, "rpa_status")
	_userRPA.RealName = field.NewString(tableName, "real_name")
	_userRPA.IDNumber = field.NewString(tableName, "id_number")
	_userRPA.IdCardFront = field.NewString(tableName, "id_front")
	_userRPA.IdCardBack = field.NewString(tableName, "id_back")
	_userRPA.IdCardInHand = field.NewString(tableName, "id_with_hand")
	_userRPA.FaceImage = field.NewString(tableName, "face_image")
	_userRPA.CreatedAt = field.NewTime(tableName, "created_at")
	_userRPA.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userRPA.fillFieldMap()

	return _userRPA
}

type userRPA struct {
	userRPADo userRPADo

	ALL          field.Asterisk
	ID           field.Int64
	UserID       field.Int64
	RpaStatus    field.Int16
	RealName     field.String
	IDNumber     field.String
	IdCardFront  field.String
	IdCardBack   field.String
	IdCardInHand field.String
	FaceImage    field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time

	fieldMap map[string]field.Expr
}

func (u userRPA) Table(newTableName string) *userRPA {
	u.userRPADo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userRPA) As(alias string) *userRPA {
	u.userRPADo.DO = *(u.userRPADo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userRPA) updateTableName(table string) *userRPA {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.RpaStatus = field.NewInt16(table, "rpa_status")
	u.RealName = field.NewString(table, "real_name")
	u.IDNumber = field.NewString(table, "id_number")
	u.IdCardFront = field.NewString(table, "id_front")
	u.IdCardBack = field.NewString(table, "id_back")
	u.IdCardInHand = field.NewString(table, "id_with_hand")
	u.FaceImage = field.NewString(table, "face_image")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userRPA) WithContext(ctx context.Context) IUserRPADo { return u.userRPADo.WithContext(ctx) }

func (u userRPA) TableName() string { return u.userRPADo.TableName() }

func (u userRPA) Alias() string { return u.userRPADo.Alias() }

func (u userRPA) Columns(cols ...field.Expr) gen.Columns { return u.userRPADo.Columns(cols...) }

func (u *userRPA) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userRPA) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 11)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["rpa_status"] = u.RpaStatus
	u.fieldMap["real_name"] = u.RealName
	u.fieldMap["id_number"] = u.IDNumber
	u.fieldMap["id_front"] = u.IdCardFront
	u.fieldMap["id_back"] = u.IdCardBack
	u.fieldMap["id_with_hand"] = u.IdCardInHand
	u.fieldMap["face_image"] = u.FaceImage
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userRPA) clone(db *gorm.DB) userRPA {
	u.userRPADo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userRPA) replaceDB(db *gorm.DB) userRPA {
	u.userRPADo.ReplaceDB(db)
	return u
}

type userRPADo struct{ gen.DO }

type IUserRPADo interface {
	gen.SubQuery
	Debug() IUserRPADo
	WithContext(ctx context.Context) IUserRPADo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserRPADo
	WriteDB() IUserRPADo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserRPADo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserRPADo
	Not(conds ...gen.Condition) IUserRPADo
	Or(conds ...gen.Condition) IUserRPADo
	Select(conds ...field.Expr) IUserRPADo
	Where(conds ...gen.Condition) IUserRPADo
	Order(conds ...field.Expr) IUserRPADo
	Distinct(cols ...field.Expr) IUserRPADo
	Omit(cols ...field.Expr) IUserRPADo
	Join(table schema.Tabler, on ...field.Expr) IUserRPADo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserRPADo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserRPADo
	Group(cols ...field.Expr) IUserRPADo
	Having(conds ...gen.Condition) IUserRPADo
	Limit(limit int) IUserRPADo
	Offset(offset int) IUserRPADo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserRPADo
	Unscoped() IUserRPADo
	Create(values ...*entity.UserRPA) error
	CreateInBatches(values []*entity.UserRPA, batchSize int) error
	Save(values ...*entity.UserRPA) error
	First() (*entity.UserRPA, error)
	Take() (*entity.UserRPA, error)
	Last() (*entity.UserRPA, error)
	Find() ([]*entity.UserRPA, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.UserRPA, err error)
	FindInBatches(result *[]*entity.UserRPA, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entity.UserRPA) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserRPADo
	Assign(attrs ...field.AssignExpr) IUserRPADo
	Joins(fields ...field.RelationField) IUserRPADo
	Preload(fields ...field.RelationField) IUserRPADo
	FirstOrInit() (*entity.UserRPA, error)
	FirstOrCreate() (*entity.UserRPA, error)
	FindByPage(offset int, limit int) (result []*entity.UserRPA, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserRPADo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userRPADo) Debug() IUserRPADo {
	return u.withDO(u.DO.Debug())
}

func (u userRPADo) WithContext(ctx context.Context) IUserRPADo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userRPADo) ReadDB() IUserRPADo {
	return u.Clauses(dbresolver.Read)
}

func (u userRPADo) WriteDB() IUserRPADo {
	return u.Clauses(dbresolver.Write)
}

func (u userRPADo) Session(config *gorm.Session) IUserRPADo {
	return u.withDO(u.DO.Session(config))
}

func (u userRPADo) Clauses(conds ...clause.Expression) IUserRPADo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userRPADo) Returning(value interface{}, columns ...string) IUserRPADo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userRPADo) Not(conds ...gen.Condition) IUserRPADo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userRPADo) Or(conds ...gen.Condition) IUserRPADo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userRPADo) Select(conds ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userRPADo) Where(conds ...gen.Condition) IUserRPADo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userRPADo) Order(conds ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userRPADo) Distinct(cols ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userRPADo) Omit(cols ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userRPADo) Join(table schema.Tabler, on ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userRPADo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userRPADo) RightJoin(table schema.Tabler, on ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userRPADo) Group(cols ...field.Expr) IUserRPADo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userRPADo) Having(conds ...gen.Condition) IUserRPADo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userRPADo) Limit(limit int) IUserRPADo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userRPADo) Offset(offset int) IUserRPADo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userRPADo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserRPADo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userRPADo) Unscoped() IUserRPADo {
	return u.withDO(u.DO.Unscoped())
}

func (u userRPADo) Create(values ...*entity.UserRPA) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userRPADo) CreateInBatches(values []*entity.UserRPA, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userRPADo) Save(values ...*entity.UserRPA) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userRPADo) First() (*entity.UserRPA, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserRPA), nil
	}
}

func (u userRPADo) Take() (*entity.UserRPA, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserRPA), nil
	}
}

func (u userRPADo) Last() (*entity.UserRPA, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserRPA), nil
	}
}

func (u userRPADo) Find() ([]*entity.UserRPA, error) {
	result, err := u.DO.Find()
	return result.([]*entity.UserRPA), err
}

func (u userRPADo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.UserRPA, err error) {
	buf := make([]*entity.UserRPA, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userRPADo) FindInBatches(result *[]*entity.UserRPA, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userRPADo) Attrs(attrs ...field.AssignExpr) IUserRPADo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userRPADo) Assign(attrs ...field.AssignExpr) IUserRPADo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userRPADo) Joins(fields ...field.RelationField) IUserRPADo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userRPADo) Preload(fields ...field.RelationField) IUserRPADo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userRPADo) FirstOrInit() (*entity.UserRPA, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserRPA), nil
	}
}

func (u userRPADo) FirstOrCreate() (*entity.UserRPA, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserRPA), nil
	}
}

func (u userRPADo) FindByPage(offset int, limit int) (result []*entity.UserRPA, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userRPADo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userRPADo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userRPADo) Delete(models ...*entity.UserRPA) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userRPADo) withDO(do gen.Dao) *userRPADo {
	u.DO = *do.(*gen.DO)
	return u
}
