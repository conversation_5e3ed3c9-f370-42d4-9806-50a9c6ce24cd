package main

import (
	"study_platform/config"
	"study_platform/entity"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	// 初始化数据库连接
	db, err := gorm.Open(mysql.Open(config.C.Database.GetDSN()))
	if err != nil {
		panic(err)
	}

	// 创建生成器实例
	g := gen.NewGenerator(gen.Config{
		OutPath:      "../../entity/query",                          // 生成代码的输出目录
		ModelPkgPath: "../../entity",                                // 模型代码的输出目录
		Mode:         gen.WithDefaultQuery | gen.WithQueryInterface, // 生成模式
		WithUnitTest: true,                                          // 生成单元测试代码
	})

	// 使用数据库连接
	g.UseDB(db)

	g.ApplyBasic(
		&entity.User{},
		&entity.UserRPA{},
		&entity.UserParams{},
		&entity.UserGroup{},
	)

	// 生成代码
	g.Execute()
}
