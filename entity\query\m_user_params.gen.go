// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"
	"study_platform/entity"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newUserParams(db *gorm.DB, opts ...gen.DOOption) userParams {
	_userParams := userParams{}

	_userParams.userParamsDo.UseDB(db, opts...)
	_userParams.userParamsDo.UseModel(&entity.UserParams{})

	tableName := _userParams.userParamsDo.TableName()
	_userParams.ALL = field.NewAsterisk(tableName)
	_userParams.ID = field.NewInt64(tableName, "id")
	_userParams.UserID = field.NewInt64(tableName, "user_id")
	_userParams.Type = field.NewInt16(tableName, "type")
	_userParams.Key = field.NewString(tableName, "key")
	_userParams.Val = field.NewString(tableName, "value")
	_userParams.IntVal = field.NewInt64(tableName, "int_val")
	_userParams.Comment = field.NewString(tableName, "comment")
	_userParams.CreatedAt = field.NewTime(tableName, "created_at")
	_userParams.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userParams.fillFieldMap()

	return _userParams
}

type userParams struct {
	userParamsDo userParamsDo

	ALL       field.Asterisk
	ID        field.Int64
	UserID    field.Int64
	Type      field.Int16
	Key       field.String
	Val       field.String
	IntVal    field.Int64
	Comment   field.String
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (u userParams) Table(newTableName string) *userParams {
	u.userParamsDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userParams) As(alias string) *userParams {
	u.userParamsDo.DO = *(u.userParamsDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userParams) updateTableName(table string) *userParams {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.Type = field.NewInt16(table, "type")
	u.Key = field.NewString(table, "key")
	u.Val = field.NewString(table, "value")
	u.IntVal = field.NewInt64(table, "int_val")
	u.Comment = field.NewString(table, "comment")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userParams) WithContext(ctx context.Context) IUserParamsDo {
	return u.userParamsDo.WithContext(ctx)
}

func (u userParams) TableName() string { return u.userParamsDo.TableName() }

func (u userParams) Alias() string { return u.userParamsDo.Alias() }

func (u userParams) Columns(cols ...field.Expr) gen.Columns { return u.userParamsDo.Columns(cols...) }

func (u *userParams) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userParams) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 9)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["type"] = u.Type
	u.fieldMap["key"] = u.Key
	u.fieldMap["value"] = u.Val
	u.fieldMap["int_val"] = u.IntVal
	u.fieldMap["comment"] = u.Comment
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userParams) clone(db *gorm.DB) userParams {
	u.userParamsDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userParams) replaceDB(db *gorm.DB) userParams {
	u.userParamsDo.ReplaceDB(db)
	return u
}

type userParamsDo struct{ gen.DO }

type IUserParamsDo interface {
	gen.SubQuery
	Debug() IUserParamsDo
	WithContext(ctx context.Context) IUserParamsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserParamsDo
	WriteDB() IUserParamsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserParamsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserParamsDo
	Not(conds ...gen.Condition) IUserParamsDo
	Or(conds ...gen.Condition) IUserParamsDo
	Select(conds ...field.Expr) IUserParamsDo
	Where(conds ...gen.Condition) IUserParamsDo
	Order(conds ...field.Expr) IUserParamsDo
	Distinct(cols ...field.Expr) IUserParamsDo
	Omit(cols ...field.Expr) IUserParamsDo
	Join(table schema.Tabler, on ...field.Expr) IUserParamsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserParamsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserParamsDo
	Group(cols ...field.Expr) IUserParamsDo
	Having(conds ...gen.Condition) IUserParamsDo
	Limit(limit int) IUserParamsDo
	Offset(offset int) IUserParamsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserParamsDo
	Unscoped() IUserParamsDo
	Create(values ...*entity.UserParams) error
	CreateInBatches(values []*entity.UserParams, batchSize int) error
	Save(values ...*entity.UserParams) error
	First() (*entity.UserParams, error)
	Take() (*entity.UserParams, error)
	Last() (*entity.UserParams, error)
	Find() ([]*entity.UserParams, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.UserParams, err error)
	FindInBatches(result *[]*entity.UserParams, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entity.UserParams) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserParamsDo
	Assign(attrs ...field.AssignExpr) IUserParamsDo
	Joins(fields ...field.RelationField) IUserParamsDo
	Preload(fields ...field.RelationField) IUserParamsDo
	FirstOrInit() (*entity.UserParams, error)
	FirstOrCreate() (*entity.UserParams, error)
	FindByPage(offset int, limit int) (result []*entity.UserParams, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserParamsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userParamsDo) Debug() IUserParamsDo {
	return u.withDO(u.DO.Debug())
}

func (u userParamsDo) WithContext(ctx context.Context) IUserParamsDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userParamsDo) ReadDB() IUserParamsDo {
	return u.Clauses(dbresolver.Read)
}

func (u userParamsDo) WriteDB() IUserParamsDo {
	return u.Clauses(dbresolver.Write)
}

func (u userParamsDo) Session(config *gorm.Session) IUserParamsDo {
	return u.withDO(u.DO.Session(config))
}

func (u userParamsDo) Clauses(conds ...clause.Expression) IUserParamsDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userParamsDo) Returning(value interface{}, columns ...string) IUserParamsDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userParamsDo) Not(conds ...gen.Condition) IUserParamsDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userParamsDo) Or(conds ...gen.Condition) IUserParamsDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userParamsDo) Select(conds ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userParamsDo) Where(conds ...gen.Condition) IUserParamsDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userParamsDo) Order(conds ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userParamsDo) Distinct(cols ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userParamsDo) Omit(cols ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userParamsDo) Join(table schema.Tabler, on ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userParamsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userParamsDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userParamsDo) Group(cols ...field.Expr) IUserParamsDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userParamsDo) Having(conds ...gen.Condition) IUserParamsDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userParamsDo) Limit(limit int) IUserParamsDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userParamsDo) Offset(offset int) IUserParamsDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userParamsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserParamsDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userParamsDo) Unscoped() IUserParamsDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userParamsDo) Create(values ...*entity.UserParams) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userParamsDo) CreateInBatches(values []*entity.UserParams, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userParamsDo) Save(values ...*entity.UserParams) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userParamsDo) First() (*entity.UserParams, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserParams), nil
	}
}

func (u userParamsDo) Take() (*entity.UserParams, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserParams), nil
	}
}

func (u userParamsDo) Last() (*entity.UserParams, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserParams), nil
	}
}

func (u userParamsDo) Find() ([]*entity.UserParams, error) {
	result, err := u.DO.Find()
	return result.([]*entity.UserParams), err
}

func (u userParamsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.UserParams, err error) {
	buf := make([]*entity.UserParams, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userParamsDo) FindInBatches(result *[]*entity.UserParams, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userParamsDo) Attrs(attrs ...field.AssignExpr) IUserParamsDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userParamsDo) Assign(attrs ...field.AssignExpr) IUserParamsDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userParamsDo) Joins(fields ...field.RelationField) IUserParamsDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userParamsDo) Preload(fields ...field.RelationField) IUserParamsDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userParamsDo) FirstOrInit() (*entity.UserParams, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserParams), nil
	}
}

func (u userParamsDo) FirstOrCreate() (*entity.UserParams, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.UserParams), nil
	}
}

func (u userParamsDo) FindByPage(offset int, limit int) (result []*entity.UserParams, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userParamsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userParamsDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userParamsDo) Delete(models ...*entity.UserParams) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userParamsDo) withDO(do gen.Dao) *userParamsDo {
	u.DO = *do.(*gen.DO)
	return u
}
