package util

import (
	"errors"

	"golang.org/x/crypto/bcrypt"
)

// BCryptPasswordEncoder BCrypt密码加密器
type BCryptPasswordEncoder struct {
	cost int // 加密强度，默认为10
}

// NewBCryptPasswordEncoder 创建新的BCrypt密码加密器
func NewBCryptPasswordEncoder(cost int) *BCryptPasswordEncoder {
	// 验证cost参数范围（4-31）
	if cost < bcrypt.MinCost || cost > bcrypt.MaxCost {
		cost = bcrypt.DefaultCost // 使用默认值10
	}
	return &BCryptPasswordEncoder{
		cost: cost,
	}
}

// 全局BCrypt密码加密器实例
var PasswordEncoder *BCryptPasswordEncoder

// 初始化全局密码加密器
func init() {
	PasswordEncoder = NewBCryptPasswordEncoder(bcrypt.DefaultCost)
}

// Encode 加密密码
func (e *BCryptPasswordEncoder) Encode(rawPassword string) (string, error) {
	if rawPassword == "" {
		return "", errors.New("密码不能为空")
	}

	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(rawPassword), e.cost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// Matches 验证密码是否匹配
func (e *BCryptPasswordEncoder) Matches(rawPassword, encodedPassword string) bool {
	if rawPassword == "" || encodedPassword == "" {
		return false
	}

	err := bcrypt.CompareHashAndPassword([]byte(encodedPassword), []byte(rawPassword))
	return err == nil
}

// UpgradeEncoding 检查是否需要重新加密（当cost发生变化时）
func (e *BCryptPasswordEncoder) UpgradeEncoding(encodedPassword string) bool {
	if encodedPassword == "" {
		return false
	}

	cost, err := bcrypt.Cost([]byte(encodedPassword))
	if err != nil {
		return true // 如果无法获取cost，建议重新加密
	}

	return cost != e.cost
}

// GetCost 获取当前加密强度
func (e *BCryptPasswordEncoder) GetCost() int {
	return e.cost
}

// SetCost 设置加密强度
func (e *BCryptPasswordEncoder) SetCost(cost int) {
	if cost >= bcrypt.MinCost && cost <= bcrypt.MaxCost {
		e.cost = cost
	}
}

// 便捷方法：使用全局实例进行密码加密
func EncodePassword(rawPassword string) (string, error) {
	return PasswordEncoder.Encode(rawPassword)
}

// 便捷方法：使用全局实例验证密码
func MatchesPassword(rawPassword, encodedPassword string) bool {
	return PasswordEncoder.Matches(rawPassword, encodedPassword)
}

// 便捷方法：检查是否需要重新加密
func NeedsUpgrade(encodedPassword string) bool {
	return PasswordEncoder.UpgradeEncoding(encodedPassword)
}

// ValidatePasswordStrength 验证密码强度（可选功能）
func ValidatePasswordStrength(password string) error {
	if len(password) < 6 {
		return errors.New("密码长度至少6位")
	}

	if len(password) > 128 {
		return errors.New("密码长度不能超过128位")
	}

	// 可以添加更多密码强度验证规则
	// 例如：必须包含大小写字母、数字、特殊字符等

	return nil
}

// GenerateRandomPassword 生成随机密码（可选功能）
func GenerateRandomPassword(length int) string {
	if length < 6 {
		length = 8 // 默认8位
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	for i := range password {
		// 简单的随机生成，实际项目中建议使用crypto/rand
		password[i] = charset[i%len(charset)]
	}

	return string(password)
}
