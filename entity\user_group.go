package entity

import (
	"time"
)

// CREATE TABLE `tb_user_group` (
//     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
//     `name` VARCHAR(100) NOT NULL COMMENT '用户组名称',
//     `comment` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '备注',
//     `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
//     `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
//     PRIMARY KEY (`id`),
//     UNIQUE KEY `uk_name` (`name`),
//     INDEX `idx_name` (`name`)
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组表';

// UserGroup 用户组实体类
type UserGroup struct {
	ID        int64      `gorm:"column:id;primaryKey" json:"id"`                    // 主键id
	Name      string     `gorm:"column:name;not null;uniqueIndex" json:"name"`      // 用户组名称
	Comment   string     `gorm:"column:comment;not null" json:"comment"`            // 备注
	CreatedAt *time.Time `gorm:"column:created_at;autoCreateTime" json:"createdAt"` // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"` // 更新时间
}

// TableName 返回表名
func (UserGroup) TableName() string {
	return "tb_user_group"
}
