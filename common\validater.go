package common

import (
	"log"

	zhLocale "github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	"github.com/go-playground/validator/v10/translations/zh"
)

var validate = validator.New()

// 创建翻译器
var zhTranslator = zhLocale.New() // 使用新的别名
var uni = ut.New(zhTranslator, zhTranslator)
var trans, _ = uni.GetTranslator("zh")

func GetValidate() *validator.Validate {
	return validate
}

func GetTrans() ut.Translator {
	return trans
}

func init() {

	// 注册默认的中文翻译
	if err := zh.RegisterDefaultTranslations(validate, trans); err != nil {
		log.Fatalf("Error registering translations: %v", err)
	}

	// 自定义错误消息
	validate.RegisterTranslation("required", trans, func(ut ut.Translator) error {
		return ut.Add("required", "{0} 是必填字段", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("required", fe.Field())
		return t
	})

	validate.RegisterTranslation("min", trans, func(ut ut.Translator) error {
		return ut.Add("min", "{0} 必须至少为 {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("min", fe.Field(), fe.Param())
		return t
	})

	validate.RegisterTranslation("max", trans, func(ut ut.Translator) error {
		return ut.Add("max", "{0} 必须至多为 {1}", true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T("max", fe.Field(), fe.Param())
		return t
	})
}

