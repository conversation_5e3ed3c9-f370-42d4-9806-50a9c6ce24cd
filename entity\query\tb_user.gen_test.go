// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"fmt"
	"study_platform/entity"
	"testing"

	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm/clause"
)

func init() {
	InitializeDB()
	err := _gen_test_db.AutoMigrate(&entity.User{})
	if err != nil {
		fmt.Printf("Error: AutoMigrate(&entity.User{}) fail: %s", err)
	}
}

func Test_userQuery(t *testing.T) {
	user := newUser(_gen_test_db)
	user = *user.As(user.TableName())
	_do := user.WithContext(context.Background()).Debug()

	primaryKey := field.NewString(user.TableName(), clause.PrimaryKey)
	_, err := _do.Unscoped().Where(primaryKey.IsNotNull()).Delete()
	if err != nil {
		t.Error("clean table <tb_user> fail:", err)
		return
	}

	_, ok := user.GetFieldByName("")
	if ok {
		t.Error("GetFieldByName(\"\") from user success")
	}

	err = _do.Create(&entity.User{})
	if err != nil {
		t.Error("create item in table <tb_user> fail:", err)
	}

	err = _do.Save(&entity.User{})
	if err != nil {
		t.Error("create item in table <tb_user> fail:", err)
	}

	err = _do.CreateInBatches([]*entity.User{{}, {}}, 10)
	if err != nil {
		t.Error("create item in table <tb_user> fail:", err)
	}

	_, err = _do.Select(user.ALL).Take()
	if err != nil {
		t.Error("Take() on table <tb_user> fail:", err)
	}

	_, err = _do.First()
	if err != nil {
		t.Error("First() on table <tb_user> fail:", err)
	}

	_, err = _do.Last()
	if err != nil {
		t.Error("First() on table <tb_user> fail:", err)
	}

	_, err = _do.Where(primaryKey.IsNotNull()).FindInBatch(10, func(tx gen.Dao, batch int) error { return nil })
	if err != nil {
		t.Error("FindInBatch() on table <tb_user> fail:", err)
	}

	err = _do.Where(primaryKey.IsNotNull()).FindInBatches(&[]*entity.User{}, 10, func(tx gen.Dao, batch int) error { return nil })
	if err != nil {
		t.Error("FindInBatches() on table <tb_user> fail:", err)
	}

	_, err = _do.Select(user.ALL).Where(primaryKey.IsNotNull()).Order(primaryKey.Desc()).Find()
	if err != nil {
		t.Error("Find() on table <tb_user> fail:", err)
	}

	_, err = _do.Distinct(primaryKey).Take()
	if err != nil {
		t.Error("select Distinct() on table <tb_user> fail:", err)
	}

	_, err = _do.Select(user.ALL).Omit(primaryKey).Take()
	if err != nil {
		t.Error("Omit() on table <tb_user> fail:", err)
	}

	_, err = _do.Group(primaryKey).Find()
	if err != nil {
		t.Error("Group() on table <tb_user> fail:", err)
	}

	_, err = _do.Scopes(func(dao gen.Dao) gen.Dao { return dao.Where(primaryKey.IsNotNull()) }).Find()
	if err != nil {
		t.Error("Scopes() on table <tb_user> fail:", err)
	}

	_, _, err = _do.FindByPage(0, 1)
	if err != nil {
		t.Error("FindByPage() on table <tb_user> fail:", err)
	}

	_, err = _do.ScanByPage(&entity.User{}, 0, 1)
	if err != nil {
		t.Error("ScanByPage() on table <tb_user> fail:", err)
	}

	_, err = _do.Attrs(primaryKey).Assign(primaryKey).FirstOrInit()
	if err != nil {
		t.Error("FirstOrInit() on table <tb_user> fail:", err)
	}

	_, err = _do.Attrs(primaryKey).Assign(primaryKey).FirstOrCreate()
	if err != nil {
		t.Error("FirstOrCreate() on table <tb_user> fail:", err)
	}

	var _a _another
	var _aPK = field.NewString(_a.TableName(), "id")

	err = _do.Join(&_a, primaryKey.EqCol(_aPK)).Scan(map[string]interface{}{})
	if err != nil {
		t.Error("Join() on table <tb_user> fail:", err)
	}

	err = _do.LeftJoin(&_a, primaryKey.EqCol(_aPK)).Scan(map[string]interface{}{})
	if err != nil {
		t.Error("LeftJoin() on table <tb_user> fail:", err)
	}

	_, err = _do.Not().Or().Clauses().Take()
	if err != nil {
		t.Error("Not/Or/Clauses on table <tb_user> fail:", err)
	}
}
