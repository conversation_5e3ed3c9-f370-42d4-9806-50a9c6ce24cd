package common

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"math/rand"
	"net"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func GetNowTime() time.Time {
	return time.Now()
}

// ParseArgs 解析命令行参数并返回一个字典
func ParseArgs() map[string]string {
	params := make(map[string]string)

	// 获取命令行参数
	args := os.Args[1:] // os.Args[0] 是程序的名称，从 os.Args[1] 开始是实际参数

	// 解析命令行参数
	for _, arg := range args {
		if strings.Contains(arg, "=") {
			// 分割参数
			parts := strings.SplitN(arg, "=", 2) // 只分割成两部分
			key := parts[0]
			value := parts[1]

			// 存储到字典中
			params[key] = value
		}
	}

	return params
}

func GenerateAdminToken(userId int64) string {
	token := GenerateRandomToken()
	return fmt.Sprintf("%d:admin:%s", userId, token)
}

func GenerateToken(userId int64) string {
	token := GenerateRandomToken()
	return fmt.Sprintf("%d:h5:%s", userId, token)
}

func GenerateRandomToken() string {
	return GenerateRandomString(32)
}

const charsetCode = "123456789"

func GenerateRandomCode(length int) string {
	// Create a new random generator
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Create a byte slice to hold random characters
	b := make([]byte, length)

	// Fill the byte slice with random characters from the charset
	for i := range b {
		b[i] = charsetCode[r.Intn(len(charsetCode))] // Use the new random generator
	}

	return string(b) // Convert the byte slice to a string and return
}

const charsetUpper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

// GenerateRandomString generates a random string of the specified length.
func GenerateRandomUpperString(length int) string {
	// Create a new random generator
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Create a byte slice to hold random characters
	b := make([]byte, length)

	// Fill the byte slice with random characters from the charset
	for i := range b {
		b[i] = charsetUpper[r.Intn(len(charsetUpper))] // Use the new random generator
	}

	return string(b) // Convert the byte slice to a string and return
}

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

// GenerateRandomString generates a random string of the specified length.
func GenerateRandomString(length int) string {
	// Create a new random generator
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Create a byte slice to hold random characters
	b := make([]byte, length)

	// Fill the byte slice with random characters from the charset
	for i := range b {
		b[i] = charset[r.Intn(len(charset))] // Use the new random generator
	}

	return string(b) // Convert the byte slice to a string and return
}

func GetUserToken(userId int64, platformType string) string {
	token := GenerateRandomToken()

	token = fmt.Sprintf("%d:%s:%s", userId, platformType, token)

	return token
}

func GetUserIdFromToken(token string) int64 {
	parts := strings.Split(token, ":")
	if len(parts) != 3 {
		return 0
	}
	userId, err := strconv.ParseInt(parts[0], 10, 64) // 修复：添加 bitSize 参数
	if err != nil {
		return 0 // 处理解析错误
	}
	return userId
}

func HashPassword(password string) string {
	// 计算 MD5 哈希
	hash := md5.Sum([]byte(password))

	// 将哈希值转换为十六进制字符串
	hashString := hex.EncodeToString(hash[:])
	return hashString
}

func GetUserInfoFromToken(token string) (int64, string) {
	parts := strings.Split(token, ":")
	if len(parts) != 3 {
		return 0, ""
	}
	userId, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return 0, ""
	}
	return userId, parts[1]
}

var ZERO_TIME = time.Time{}

func IsZeroTime(time time.Time) bool {
	return time == ZERO_TIME
}

func IsNotZeroTime(time time.Time) bool {
	return time != ZERO_TIME
}

// MaskPhoneNumber 对手机号进行脱敏处理
// 保留前3位和后4位，中间部分用星号替换
// 例如：13812345678 -> 138****5678
func MaskPhoneNumber(phoneNumber string) string {
	// 检查手机号长度是否合法
	length := len(phoneNumber)
	if length < 7 {
		// 如果手机号长度小于7，无法进行有效脱敏，直接返回原值
		return phoneNumber
	}

	// 确定要保留的前缀和后缀长度
	prefixLen := 3
	suffixLen := 4

	// 如果手机号长度不足以保留指定的前缀和后缀，调整保留长度
	if length < prefixLen+suffixLen {
		prefixLen = 1
		suffixLen = 1
	}

	// 计算中间需要用星号替换的部分长度
	maskLen := length - prefixLen - suffixLen

	// 构建脱敏后的手机号
	prefix := phoneNumber[:prefixLen]
	suffix := phoneNumber[length-suffixLen:]
	mask := strings.Repeat("*", maskLen)

	return prefix + mask + suffix
}

func GetTimeFormat() string {
	return "2006-01-02 15:04:05"
}

func NewOrderId() int64 {
	// 使用本地时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		loc = time.Local
	}
	now := time.Now().In(loc)

	// 获取年份后两位作为前缀
	yearSuffix := now.Format("06") // 例如 2023 年得到 "23"

	// 计算今年的第一天
	firstDayOfYear := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, loc)

	// 计算从今年开始到现在的秒数
	secondsOfYear := now.Unix() - firstDayOfYear.Unix()

	// 将今年的秒数转为字符串
	// 一年最多有 31,536,000 秒 (365天)，最多是8位数字
	secondsStr := fmt.Sprintf("%d", secondsOfYear)

	// 使用2位随机数确保唯一性
	randomStr := GenerateRandomCode(2)

	// 组合成订单ID：年份后2位 + 今年的秒数 + 随机数(2位)
	// 例如：23+12345678+12，表示2023年，今年的第12345678秒，随机数12
	orderId := yearSuffix + secondsStr + randomStr
	orderIdInt, err := strconv.ParseInt(orderId, 10, 64)
	if err != nil {
		return 0
	}
	return orderIdInt
}

// 获取本机的 内网IP地址
func GetLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}

	for _, addr := range addrs {
		// 检查IP地址类型
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			// 只获取IPv4地址
			if ipnet.IP.To4() != nil {
				// 检查是否是内网IP
				ip := ipnet.IP.To4()
				// 判断是否是内网IP地址
				// 10.0.0.0/8
				// **********/12
				// ***********/16
				if ip[0] == 10 ||
					(ip[0] == 172 && ip[1] >= 16 && ip[1] <= 31) ||
					(ip[0] == 192 && ip[1] == 168) {
					return ip.String()
				}
			}
		}
	}
	return ""
}

func MaskPasswordInJSON(jsonStr string) string {
	// 正则表达式匹配 "password":"任何值" 模式
	re := regexp.MustCompile(`"password":"[^"]*"`)

	// 替换为 "password":"***"
	maskedJSON := re.ReplaceAllString(jsonStr, `"password":"***"`)

	return maskedJSON
}

// 使用示例
func MaskSensitiveData(reqBody string) string {
	if strings.Contains(reqBody, `"password"`) {
		return MaskPasswordInJSON(reqBody)
	}
	return reqBody
}
